<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xuxueli</groupId>
        <artifactId>xxl-job</artifactId>
        <version>3.1.2-SNAPSHOT</version>
    </parent>
    <artifactId>xxl-job-executor-samples</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>xxl-job-executor-sample-frameless</module>
        <module>xxl-job-executor-sample-springboot</module>
        <module>xxl-job-executor-sample-springboot-ai</module>
    </modules>

    <properties>
        <maven.install.skip>true</maven.install.skip>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

</project>